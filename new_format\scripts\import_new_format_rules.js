const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// === Supabase配置 ===
const supabaseUrl = 'https://zczyxyoklmxudtqqekfh.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpjenl4eW9rbG14dWR0cXFla2ZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNDMwNTYsImV4cCI6MjA2NzgxOTA1Nn0.Q1iFmfei6N_UcDZa99sr1j6frNgzDAsFhEeQUzhskiI';
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * 读取新格式的JSON文件并转换为数据库格式
 */
function loadNewFormatRules() {
    console.log('📂 开始读取新格式的JSON文件...\n');
    
    // 新格式文件列表
    const newFormatFiles = [
        'ACC_PURPOSE_LGT.json',
        'ACC_SPECIAL_ENTITY_LGT.json',
        'SOW_BUSINESS_OWNER_LGT.json',
        'SOW_EMPLOY_LGT.json',
        'SOW_INHERITANCE_LGT.json',
        'SOW_INVESTMENT_LGT.json',
        'SOW_SALES_ASSETS_LGT.json',
        'SOW_SALES_BUSINESS_LGT.json',
        'SOW_SALES_REALESTATE_LGT.json',
        'UBO_ASSET_COMPOSITION_LGT.json',
        'UBO_FAMILY_LGT.json'
    ];
    
    const allRules = [];
    
    newFormatFiles.forEach(filename => {
        // 文件路径指向新格式文件的正确位置
        const filePath = path.join(__dirname, '..', '..', '..', 'LGT_Rule_Mgmt_vs0714(1)', 'LGT_Rule_Mgmt_newformat', filename);
        
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  文件不存在: ${filename}`);
            return;
        }
        
        try {
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const data = JSON.parse(fileContent);
            
            if (data.rules && Array.isArray(data.rules)) {
                data.rules.forEach(rule => {
                    // 转换为数据库格式，rule_name → name
                    const dbRule = {
                        name: rule.rule_name,
                        key_name: rule.key_name,
                        description: rule.description,
                        requirement_type: rule.requirement_type,
                        rule_condition: rule.rule_condition,
                        story_type: rule.story_type,
                        sub_type: rule.sub_type,
                        status: rule.status,
                        risk: rule.risk,
                        priority: rule.priority,
                        reference_key_names: rule.reference_key_names || null,
                        red_flags: rule.red_flags || null
                    };
                    
                    allRules.push(dbRule);
                });
                
                console.log(`✅ 已读取 ${filename}: ${data.rules.length} 条规则`);
            } else {
                console.log(`⚠️  ${filename} 格式不正确，缺少 rules 数组`);
            }
        } catch (error) {
            console.log(`❌ 读取文件 ${filename} 时出错:`, error.message);
        }
    });
    
    console.log(`\n📊 总共读取到 ${allRules.length} 条规则\n`);
    return allRules;
}

/**
 * 检查数据库表结构
 */
async function checkTableStructure() {
    console.log('🔍 检查数据库表结构...');
    
    try {
        const { data, error } = await supabase
            .from('rules')
            .select('*')
            .limit(1);
        
        if (error) {
            if (error.message.includes('relation "rules" does not exist')) {
                console.log('❌ rules表不存在，请先执行SQL创建表');
                console.log('📁 SQL文件位置: new_format/sql/create_rules_table_new_format.sql');
                return false;
            } else {
                console.log('❌ 检查表结构时出错:', error.message);
                return false;
            }
        }
        
        console.log('✅ rules表存在');
        return true;
    } catch (err) {
        console.log('❌ 检查表结构时出错:', err.message);
        return false;
    }
}

/**
 * 清空现有数据
 */
async function clearExistingData() {
    console.log('🗑️  清空现有数据...');
    
    try {
        const { error } = await supabase
            .from('rules')
            .delete()
            .neq('id', 0); // 删除所有记录
        
        if (error) {
            console.log('❌ 清空数据时出错:', error.message);
            return false;
        }
        
        console.log('✅ 现有数据已清空');
        return true;
    } catch (err) {
        console.log('❌ 清空数据时出错:', err.message);
        return false;
    }
}

/**
 * 批量插入规则数据
 */
async function insertRules(rules) {
    console.log('📥 开始插入规则数据...');
    
    const batchSize = 50; // 每批插入50条
    const totalBatches = Math.ceil(rules.length / batchSize);
    
    for (let i = 0; i < totalBatches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, rules.length);
        const batch = rules.slice(start, end);
        
        try {
            const { data, error } = await supabase
                .from('rules')
                .insert(batch);
            
            if (error) {
                console.log(`❌ 插入第 ${i + 1} 批数据时出错:`, error.message);
                return false;
            }
            
            console.log(`✅ 已插入第 ${i + 1}/${totalBatches} 批数据 (${batch.length} 条)`);
        } catch (err) {
            console.log(`❌ 插入第 ${i + 1} 批数据时出错:`, err.message);
            return false;
        }
    }
    
    console.log(`🎉 所有数据插入完成！总共插入 ${rules.length} 条规则`);
    return true;
}

/**
 * 验证插入结果
 */
async function verifyInsertedData() {
    console.log('\n📊 验证插入结果...');
    
    try {
        const { data, error, count } = await supabase
            .from('rules')
            .select('*', { count: 'exact' })
            .limit(5);
        
        if (error) {
            console.log('❌ 验证数据时出错:', error.message);
            return;
        }
        
        console.log(`📈 数据库中现有 ${count} 条规则`);
        
        if (data && data.length > 0) {
            console.log('\n前5条记录示例:');
            data.forEach((record, index) => {
                console.log(`${index + 1}. ${record.name} (${record.story_type}/${record.sub_type})`);
            });
        }
        
        // 按story_type统计
        const { data: summary } = await supabase
            .from('rules_summary')
            .select('*');
        
        if (summary) {
            console.log('\n📊 按类型统计:');
            summary.forEach(item => {
                console.log(`  ${item.story_type}/${item.sub_type}/${item.requirement_type}: ${item.rule_count} 条`);
            });
        }
        
    } catch (err) {
        console.log('❌ 验证数据时出错:', err.message);
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 开始导入新格式规则数据到Supabase...\n');
    
    // 1. 读取新格式数据
    const rules = loadNewFormatRules();
    if (rules.length === 0) {
        console.log('❌ 没有读取到任何规则数据，退出');
        return;
    }
    
    // 2. 检查表结构
    const tableExists = await checkTableStructure();
    if (!tableExists) {
        return;
    }
    
    // 3. 清空现有数据
    const cleared = await clearExistingData();
    if (!cleared) {
        return;
    }
    
    // 4. 插入新数据
    const inserted = await insertRules(rules);
    if (!inserted) {
        return;
    }
    
    // 5. 验证结果
    await verifyInsertedData();
    
    console.log('\n🎉 导入完成！');
}

// 执行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { loadNewFormatRules, insertRules };
