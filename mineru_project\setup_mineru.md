# MinerU 环境设置指南

## 前提条件：安装 Conda

如果您还没有安装 conda，请先安装 Miniconda 或 Anaconda：

### 选项1：安装 Miniconda（推荐，体积小）
1. 访问：https://docs.conda.io/en/latest/miniconda.html
2. 下载 Windows 版本的 Miniconda3
3. 运行安装程序，选择 "Add Miniconda3 to PATH" 选项

### 选项2：安装 Anaconda（功能全面）
1. 访问：https://www.anaconda.com/products/distribution
2. 下载 Windows 版本
3. 运行安装程序

## MinerU 环境设置步骤

安装完 conda 后，打开 **Anaconda Prompt** 或 **命令提示符**，执行以下命令：

### 1. 创建 Python 3.12 环境
```bash
conda create -n mineru python=3.12 -y
```

### 2. 激活环境
```bash
conda activate mineru
```

### 3. 安装 magic-pdf
```bash
pip install -U "magic-pdf[full]" -i https://mirrors.aliyun.com/pypi/simple
```

**注意：** `-i https://mirrors.aliyun.com/pypi/simple` 是使用阿里云镜像源加速下载

### 4. 验证安装
```bash
python -c "import magic_pdf; print('MinerU 安装成功！')"
```

## 替代镜像源

如果阿里云源有问题，可以尝试其他国内镜像源：

### 清华源
```bash
pip install -U "magic-pdf[full]" -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 豆瓣源
```bash
pip install -U "magic-pdf[full]" -i https://pypi.douban.com/simple
```

## 使用环境

每次使用 MinerU 时，记得先激活环境：
```bash
conda activate mineru
```

## 项目文件夹

已为您创建项目文件夹：`mineru_project/`

您可以在这个文件夹中存放相关的代码和文档。
