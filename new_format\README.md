# LGT 规则管理系统 - 新格式支持

这个文件夹包含了支持同事提供的新JSON格式的数据库表结构和脚本。

## 新格式特点

### JSON 文件结构
```json
{
  "rules": [
    {
      "id": 1,
      "rule_name": "Account Funding Bank",
      "key_name": "acc_funding_bank",
      "description": "规则描述...",
      "requirement_type": "mandatory",
      "rule_condition": "Not Applicable",
      "story_type": "ACC_PURPOSE",
      "sub_type": "COMPLETENESS",
      "status": "active",
      "risk": 0,
      "priority": 0,
      "reference_key_names": ["..."], // 可选
      "red_flags": ["..."] // 可选
    }
  ]
}
```

### 数据库表结构
- `name` (TEXT) - 规则名称 (映射自 rule_name)
- `key_name` (TEXT) - 规则唯一标识
- `description` (TEXT) - 规则描述
- `requirement_type` (TEXT) - 需求类型: mandatory/conditional
- `rule_condition` (TEXT) - 规则条件
- `story_type` (TEXT) - 业务场景类型
- `sub_type` (TEXT) - 规则子类型: COMPLETENESS/LOGICAL_VALIDATION
- `status` (TEXT) - 状态: active/inactive
- `risk` (INTEGER) - 风险等级
- `priority` (INTEGER) - 优先级
- `reference_key_names` (JSONB) - 引用的其他规则键名
- `red_flags` (JSONB) - 红旗标识

## 使用步骤

### 1. 安装依赖
```bash
cd new_format
npm install
```

### 2. 检查兼容性
```bash
npm run check
```
这会检查：
- 新格式JSON文件是否存在和有效
- 数据库表结构是否兼容
- 现有数据情况

### 3. 创建/更新数据库表
在 Supabase SQL 编辑器中执行：
```sql
-- 执行这个文件来创建新格式的表结构
sql/create_rules_table_new_format.sql
```

### 4. 导入数据
```bash
npm run import
```

## 文件说明

### SQL 文件
- `sql/create_rules_table_new_format.sql` - 创建新格式的数据库表结构

### 脚本文件
- `scripts/check_new_format_data.js` - 检查新格式数据和数据库兼容性
- `scripts/import_new_format_rules.js` - 导入新格式数据到 Supabase

### 配置文件
- `package.json` - Node.js 项目配置
- `README.md` - 使用说明

## 主要改进

### 相比旧格式的优势
1. **扁平化结构** - 所有字段都在顶层，便于查询和索引
2. **标准化字段名** - 使用一致的命名规范
3. **类型安全** - 明确的字段类型和约束
4. **可选字段支持** - reference_key_names 和 red_flags 为可选
5. **更好的索引** - 为关键字段创建了专门的索引

### 字段映射
- `rule_name` → `name`
- `sub_type` → 保持不变 (COMPLETENESS/LOGICAL_VALIDATION)
- `status` → 保持不变 (active/inactive)
- 其他字段基本保持一致

## 注意事项

1. **数据备份** - 在执行表结构更新前，建议备份现有数据
2. **批量插入** - 脚本使用批量插入提高性能
3. **错误处理** - 包含完整的错误处理和验证
4. **索引优化** - 为常用查询字段创建了索引

## 故障排除

### 常见问题
1. **表不存在** - 执行 SQL 文件创建表
2. **字段不匹配** - 检查表结构是否为新格式
3. **文件路径错误** - 确保 JSON 文件在正确位置
4. **权限问题** - 检查 Supabase 连接配置

### 联系支持
如有问题，请检查：
1. Supabase 连接配置是否正确
2. JSON 文件格式是否符合要求
3. 数据库表结构是否匹配
