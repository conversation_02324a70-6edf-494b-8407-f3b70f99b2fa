const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// === Supabase配置 ===
const supabaseUrl = 'https://zczyxyoklmxudtqqekfh.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpjenl4eW9rbG14dWR0cXFla2ZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNDMwNTYsImV4cCI6MjA2NzgxOTA1Nn0.Q1iFmfei6N_UcDZa99sr1j6frNgzDAsFhEeQUzhskiI';
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * 自动检测新格式JSON文件的位置
 */
function detectNewFormatFiles() {
    console.log('🔍 自动检测新格式JSON文件位置...\n');
    
    const newFormatFiles = [
        'ACC_PURPOSE_LGT.json',
        'ACC_SPECIAL_ENTITY_LGT.json',
        'SOW_BUSINESS_OWNER_LGT.json',
        'SOW_EMPLOY_LGT.json',
        'SOW_INHERITANCE_LGT.json',
        'SOW_INVESTMENT_LGT.json',
        'SOW_SALES_ASSETS_LGT.json',
        'SOW_SALES_BUSINESS_LGT.json',
        'SOW_SALES_REALESTATE_LGT.json',
        'UBO_ASSET_COMPOSITION_LGT.json',
        'UBO_FAMILY_LGT.json'
    ];
    
    // 可能的文件路径
    const possiblePaths = [
        // 当前目录
        __dirname + '/../..',
        // 新格式目录
        path.join(__dirname, '..', '..', '..', 'LGT_Rule_Mgmt_vs0714(1)', 'LGT_Rule_Mgmt_newformat'),
        // 用户桌面的新格式目录
        'C:\\Users\\<USER>\\Desktop\\LGT_Rule_Mgmt_vs0714(1)\\LGT_Rule_Mgmt_newformat',
        // 相对路径
        path.join(__dirname, '..', '..', '..', '..', 'LGT_Rule_Mgmt_vs0714(1)', 'LGT_Rule_Mgmt_newformat')
    ];
    
    let foundPath = null;
    
    for (const basePath of possiblePaths) {
        console.log(`检查路径: ${basePath}`);
        
        // 检查第一个文件是否存在
        const testFile = path.join(basePath, newFormatFiles[0]);
        if (fs.existsSync(testFile)) {
            // 验证是否为新格式
            try {
                const content = fs.readFileSync(testFile, 'utf8');
                const data = JSON.parse(content);
                
                if (data.rules && Array.isArray(data.rules) && data.rules.length > 0) {
                    const firstRule = data.rules[0];
                    if (firstRule.rule_name && firstRule.key_name && firstRule.story_type) {
                        foundPath = basePath;
                        console.log(`✅ 找到新格式文件路径: ${basePath}\n`);
                        break;
                    }
                }
            } catch (error) {
                console.log(`❌ 文件格式检查失败: ${error.message}`);
            }
        }
    }
    
    if (!foundPath) {
        console.log('❌ 未找到新格式JSON文件');
        console.log('请确保以下文件存在于正确位置:');
        newFormatFiles.forEach(file => console.log(`  - ${file}`));
        return null;
    }
    
    return { basePath: foundPath, files: newFormatFiles };
}

/**
 * 读取新格式的JSON文件并转换为数据库格式
 */
function loadNewFormatRules(detectedFiles) {
    console.log('📂 读取新格式的JSON文件...\n');
    
    const allRules = [];
    
    detectedFiles.files.forEach(filename => {
        const filePath = path.join(detectedFiles.basePath, filename);
        
        try {
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const data = JSON.parse(fileContent);
            
            if (data.rules && Array.isArray(data.rules)) {
                data.rules.forEach(rule => {
                    // 转换为数据库格式，rule_name → name
                    const dbRule = {
                        name: rule.rule_name,
                        key_name: rule.key_name,
                        description: rule.description,
                        requirement_type: rule.requirement_type,
                        rule_condition: rule.rule_condition,
                        story_type: rule.story_type,
                        sub_type: rule.sub_type,
                        status: rule.status,
                        risk: rule.risk,
                        priority: rule.priority,
                        reference_key_names: rule.reference_key_names || null,
                        red_flags: rule.red_flags || null
                    };
                    
                    allRules.push(dbRule);
                });
                
                console.log(`✅ 已读取 ${filename}: ${data.rules.length} 条规则`);
            } else {
                console.log(`⚠️  ${filename} 格式不正确，缺少 rules 数组`);
            }
        } catch (error) {
            console.log(`❌ 读取文件 ${filename} 时出错:`, error.message);
        }
    });
    
    console.log(`\n📊 总共读取到 ${allRules.length} 条规则\n`);
    return allRules;
}

/**
 * 检查数据库表结构
 */
async function checkTableStructure() {
    console.log('🔍 检查数据库表结构...');
    
    try {
        const { data, error } = await supabase
            .from('rules')
            .select('*')
            .limit(1);
        
        if (error) {
            if (error.message.includes('relation "rules" does not exist')) {
                console.log('❌ rules表不存在，请先执行SQL创建表');
                console.log('📁 SQL文件位置: sql_files/update_rules_table_for_new_format.sql');
                return false;
            } else {
                console.log('❌ 检查表结构时出错:', error.message);
                return false;
            }
        }
        
        console.log('✅ rules表存在');
        return true;
    } catch (err) {
        console.log('❌ 检查表结构时出错:', err.message);
        return false;
    }
}

/**
 * 清空现有数据
 */
async function clearExistingData() {
    console.log('🗑️  清空现有数据...');
    
    try {
        const { error } = await supabase
            .from('rules')
            .delete()
            .neq('id', 0); // 删除所有记录
        
        if (error) {
            console.log('❌ 清空数据时出错:', error.message);
            return false;
        }
        
        console.log('✅ 现有数据已清空');
        return true;
    } catch (err) {
        console.log('❌ 清空数据时出错:', err.message);
        return false;
    }
}

/**
 * 批量插入规则数据
 */
async function insertRules(rules) {
    console.log('📥 开始插入规则数据...');
    
    const batchSize = 50; // 每批插入50条
    const totalBatches = Math.ceil(rules.length / batchSize);
    
    for (let i = 0; i < totalBatches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, rules.length);
        const batch = rules.slice(start, end);
        
        try {
            const { data, error } = await supabase
                .from('rules')
                .insert(batch);
            
            if (error) {
                console.log(`❌ 插入第 ${i + 1} 批数据时出错:`, error.message);
                return false;
            }
            
            console.log(`✅ 已插入第 ${i + 1}/${totalBatches} 批数据 (${batch.length} 条)`);
        } catch (err) {
            console.log(`❌ 插入第 ${i + 1} 批数据时出错:`, err.message);
            return false;
        }
    }
    
    console.log(`🎉 所有数据插入完成！总共插入 ${rules.length} 条规则`);
    return true;
}

/**
 * 验证插入结果
 */
async function verifyInsertedData() {
    console.log('\n📊 验证插入结果...');
    
    try {
        const { data, error, count } = await supabase
            .from('rules')
            .select('*', { count: 'exact' })
            .limit(5);
        
        if (error) {
            console.log('❌ 验证数据时出错:', error.message);
            return;
        }
        
        console.log(`📈 数据库中现有 ${count} 条规则`);
        
        if (data && data.length > 0) {
            console.log('\n前5条记录示例:');
            data.forEach((record, index) => {
                console.log(`${index + 1}. ${record.name} (${record.story_type}/${record.sub_type})`);
            });
        }
        
    } catch (err) {
        console.log('❌ 验证数据时出错:', err.message);
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 自动检测并导入新格式规则数据到Supabase...\n');
    
    // 1. 自动检测文件位置
    const detectedFiles = detectNewFormatFiles();
    if (!detectedFiles) {
        return;
    }
    
    // 2. 读取新格式数据
    const rules = loadNewFormatRules(detectedFiles);
    if (rules.length === 0) {
        console.log('❌ 没有读取到任何规则数据，退出');
        return;
    }
    
    // 3. 检查表结构
    const tableExists = await checkTableStructure();
    if (!tableExists) {
        return;
    }
    
    // 4. 清空现有数据
    const cleared = await clearExistingData();
    if (!cleared) {
        return;
    }
    
    // 5. 插入新数据
    const inserted = await insertRules(rules);
    if (!inserted) {
        return;
    }
    
    // 6. 验证结果
    await verifyInsertedData();
    
    console.log('\n🎉 导入完成！');
}

// 执行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { detectNewFormatFiles, loadNewFormatRules, insertRules };
