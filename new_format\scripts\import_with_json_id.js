const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// === Supabase配置 ===
const supabaseUrl = 'https://zczyxyoklmxudtqqekfh.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpjenl4eW9rbG14dWR0cXFla2ZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNDMwNTYsImV4cCI6MjA2NzgxOTA1Nn0.Q1iFmfei6N_UcDZa99sr1j6frNgzDAsFhEeQUzhskiI';
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * 读取新格式的JSON文件并保留原始ID
 */
function loadNewFormatRulesWithId() {
    console.log('📂 读取新格式的JSON文件（保留原始ID）...\n');
    
    const newFormatFiles = [
        'ACC_PURPOSE_LGT.json',
        'ACC_SPECIAL_ENTITY_LGT.json',
        'SOW_BUSINESS_OWNER_LGT.json',
        'SOW_EMPLOY_LGT.json',
        'SOW_INHERITANCE_LGT.json',
        'SOW_INVESTMENT_LGT.json',
        'SOW_SALES_ASSETS_LGT.json',
        'SOW_SALES_BUSINESS_LGT.json',
        'SOW_SALES_REALESTATE_LGT.json',
        'UBO_ASSET_COMPOSITION_LGT.json',
        'UBO_FAMILY_LGT.json'
    ];
    
    const allRules = [];
    let globalIdOffset = 0; // 用于处理可能的ID冲突
    
    newFormatFiles.forEach((filename, fileIndex) => {
        // 直接从当前工作目录读取文件
        const filePath = path.join(process.cwd(), filename);
        
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  文件不存在: ${filename}`);
            return;
        }
        
        try {
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const data = JSON.parse(fileContent);
            
            if (data.rules && Array.isArray(data.rules)) {
                data.rules.forEach(rule => {
                    // 保留JSON中的ID，但确保全局唯一
                    const originalId = rule.id;
                    const adjustedId = originalId + (fileIndex * 1000); // 为每个文件分配1000个ID空间
                    
                    const dbRule = {
                        id: adjustedId, // 使用调整后的ID确保唯一性
                        name: rule.rule_name, // rule_name → name
                        key_name: rule.key_name,
                        description: rule.description,
                        requirement_type: rule.requirement_type,
                        rule_condition: rule.rule_condition || '',
                        story_type: rule.story_type,
                        sub_type: rule.sub_type,
                        status: rule.status,
                        risk: rule.risk || 0,
                        priority: rule.priority || 0,
                        reference_key_names: rule.reference_key_names || null,
                        red_flags: rule.red_flags || null
                    };
                    
                    allRules.push(dbRule);
                });
                
                console.log(`✅ 已读取 ${filename}: ${data.rules.length} 条规则 (ID范围: ${fileIndex * 1000 + 1}-${fileIndex * 1000 + data.rules.length})`);
            } else {
                console.log(`⚠️  ${filename} 格式不正确，缺少 rules 数组`);
            }
        } catch (error) {
            console.log(`❌ 读取文件 ${filename} 时出错:`, error.message);
        }
    });
    
    console.log(`\n📊 总共读取到 ${allRules.length} 条规则\n`);
    
    // 检查ID唯一性
    const ids = allRules.map(r => r.id);
    const uniqueIds = [...new Set(ids)];
    if (ids.length !== uniqueIds.length) {
        console.log('⚠️  检测到重复ID，将重新分配...');
        allRules.forEach((rule, index) => {
            rule.id = index + 1;
        });
    }
    
    return allRules;
}

/**
 * 检查数据库表结构
 */
async function checkTableStructure() {
    console.log('🔍 检查数据库表结构...');
    
    try {
        const { data, error } = await supabase
            .from('rules')
            .select('*')
            .limit(1);
        
        if (error) {
            if (error.message.includes('relation "rules" does not exist')) {
                console.log('❌ rules表不存在，请先执行SQL创建表');
                console.log('📁 SQL文件位置: sql_files/create_rules_table_with_json_id.sql');
                return false;
            } else {
                console.log('❌ 检查表结构时出错:', error.message);
                return false;
            }
        }
        
        console.log('✅ rules表存在');
        return true;
    } catch (err) {
        console.log('❌ 检查表结构时出错:', err.message);
        return false;
    }
}

/**
 * 清空现有数据
 */
async function clearExistingData() {
    console.log('🗑️  清空现有数据...');
    
    try {
        const { error } = await supabase
            .from('rules')
            .delete()
            .neq('id', 0); // 删除所有记录
        
        if (error) {
            console.log('❌ 清空数据时出错:', error.message);
            return false;
        }
        
        console.log('✅ 现有数据已清空');
        return true;
    } catch (err) {
        console.log('❌ 清空数据时出错:', err.message);
        return false;
    }
}

/**
 * 批量插入规则数据（保留ID）
 */
async function insertRulesWithId(rules) {
    console.log('📥 开始插入规则数据（保留原始ID）...');
    
    const batchSize = 20; // 适中的批次大小
    const totalBatches = Math.ceil(rules.length / batchSize);
    
    for (let i = 0; i < totalBatches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, rules.length);
        const batch = rules.slice(start, end);
        
        try {
            const { data, error } = await supabase
                .from('rules')
                .insert(batch);
            
            if (error) {
                console.log(`❌ 插入第 ${i + 1} 批数据时出错:`, error.message);
                
                // 尝试逐条插入以找出问题
                console.log('尝试逐条插入以诊断问题...');
                for (const rule of batch) {
                    try {
                        const { error: singleError } = await supabase
                            .from('rules')
                            .insert([rule]);
                        
                        if (singleError) {
                            console.log(`❌ 插入规则 ID:${rule.id} "${rule.name}" 失败:`, singleError.message);
                        } else {
                            console.log(`✅ 成功插入规则 ID:${rule.id} "${rule.name}"`);
                        }
                    } catch (singleErr) {
                        console.log(`❌ 插入规则 ID:${rule.id} "${rule.name}" 异常:`, singleErr.message);
                    }
                }
            } else {
                console.log(`✅ 已插入第 ${i + 1}/${totalBatches} 批数据 (${batch.length} 条)`);
            }
        } catch (err) {
            console.log(`❌ 插入第 ${i + 1} 批数据时异常:`, err.message);
        }
        
        // 添加延迟以避免速率限制
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log(`🎉 所有数据插入完成！`);
    return true;
}

/**
 * 验证插入结果
 */
async function verifyInsertedData() {
    console.log('\n📊 验证插入结果...');
    
    try {
        const { data, error, count } = await supabase
            .from('rules')
            .select('*', { count: 'exact' })
            .order('id')
            .limit(10);
        
        if (error) {
            console.log('❌ 验证数据时出错:', error.message);
            return;
        }
        
        console.log(`📈 数据库中现有 ${count} 条规则`);
        
        if (data && data.length > 0) {
            console.log('\n前10条记录示例:');
            data.forEach((record, index) => {
                console.log(`${index + 1}. ID:${record.id} ${record.name} (${record.story_type}/${record.sub_type})`);
            });
            
            // 显示ID范围
            const minId = Math.min(...data.map(r => r.id));
            const maxId = Math.max(...data.map(r => r.id));
            console.log(`\n📊 ID范围: ${minId} - ${maxId}`);
        }
        
        // 按story_type统计
        const { data: summary } = await supabase
            .from('rules_summary')
            .select('*');
        
        if (summary) {
            console.log('\n📊 按类型统计:');
            summary.forEach(item => {
                console.log(`  ${item.story_type}/${item.sub_type}/${item.requirement_type}: ${item.rule_count} 条 (ID: ${item.min_id}-${item.max_id})`);
            });
        }
        
    } catch (err) {
        console.log('❌ 验证数据时出错:', err.message);
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 导入新格式规则数据到Supabase（保留JSON ID）...\n');
    
    // 1. 读取新格式数据
    const rules = loadNewFormatRulesWithId();
    if (rules.length === 0) {
        console.log('❌ 没有读取到任何规则数据，退出');
        return;
    }
    
    // 2. 检查表结构
    const tableExists = await checkTableStructure();
    if (!tableExists) {
        return;
    }
    
    // 3. 清空现有数据
    const cleared = await clearExistingData();
    if (!cleared) {
        return;
    }
    
    // 4. 插入新数据
    const inserted = await insertRulesWithId(rules);
    if (!inserted) {
        return;
    }
    
    // 5. 验证结果
    await verifyInsertedData();
    
    console.log('\n🎉 导入完成！');
}

// 执行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { loadNewFormatRulesWithId, insertRulesWithId };
