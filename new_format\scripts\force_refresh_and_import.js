const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// === Supabase配置 ===
const supabaseUrl = 'https://zczyxyoklmxudtqqekfh.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpjenl4eW9rbG14dWR0cXFla2ZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNDMwNTYsImV4cCI6MjA2NzgxOTA1Nn0.Q1iFmfei6N_UcDZa99sr1j6frNgzDAsFhEeQUzhskiI';

// 创建新的客户端实例以避免缓存问题
const supabase = createClient(supabaseUrl, supabaseKey, {
    db: {
        schema: 'public'
    },
    auth: {
        persistSession: false
    }
});

/**
 * 使用原生SQL插入数据以避免缓存问题
 */
async function insertRulesWithSQL(rules) {
    console.log('📥 使用原生SQL插入规则数据...');
    
    const batchSize = 10; // 减小批次大小
    const totalBatches = Math.ceil(rules.length / batchSize);
    
    for (let i = 0; i < totalBatches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, rules.length);
        const batch = rules.slice(start, end);
        
        // 构建SQL插入语句
        const values = batch.map(rule => {
            const name = rule.name.replace(/'/g, "''");
            const description = rule.description.replace(/'/g, "''");
            const ruleCondition = rule.rule_condition ? rule.rule_condition.replace(/'/g, "''") : '';
            const referenceKeyNames = rule.reference_key_names ? JSON.stringify(rule.reference_key_names).replace(/'/g, "''") : 'null';
            const redFlags = rule.red_flags ? JSON.stringify(rule.red_flags).replace(/'/g, "''") : 'null';
            
            return `(
                '${name}',
                '${rule.key_name}',
                '${description}',
                '${rule.requirement_type}',
                '${ruleCondition}',
                '${rule.story_type}',
                '${rule.sub_type}',
                '${rule.status}',
                ${rule.risk},
                ${rule.priority},
                ${referenceKeyNames === 'null' ? 'null' : `'${referenceKeyNames}'::jsonb`},
                ${redFlags === 'null' ? 'null' : `'${redFlags}'::jsonb`}
            )`;
        }).join(',\n');
        
        const sql = `
            INSERT INTO rules (
                name, key_name, description, requirement_type, rule_condition,
                story_type, sub_type, status, risk, priority,
                reference_key_names, red_flags
            ) VALUES ${values};
        `;
        
        try {
            const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
            
            if (error) {
                // 如果RPC不可用，尝试直接使用from().insert()
                console.log(`尝试使用标准插入方法...`);
                
                const { data: insertData, error: insertError } = await supabase
                    .from('rules')
                    .insert(batch);
                
                if (insertError) {
                    console.log(`❌ 插入第 ${i + 1} 批数据时出错:`, insertError.message);
                    
                    // 尝试逐条插入
                    console.log('尝试逐条插入...');
                    for (const rule of batch) {
                        try {
                            const { error: singleError } = await supabase
                                .from('rules')
                                .insert([rule]);
                            
                            if (singleError) {
                                console.log(`❌ 插入规则 "${rule.name}" 失败:`, singleError.message);
                            }
                        } catch (singleErr) {
                            console.log(`❌ 插入规则 "${rule.name}" 异常:`, singleErr.message);
                        }
                    }
                } else {
                    console.log(`✅ 已插入第 ${i + 1}/${totalBatches} 批数据 (${batch.length} 条)`);
                }
            } else {
                console.log(`✅ 已插入第 ${i + 1}/${totalBatches} 批数据 (${batch.length} 条)`);
            }
        } catch (err) {
            console.log(`❌ 插入第 ${i + 1} 批数据时异常:`, err.message);
            
            // 尝试逐条插入作为备选方案
            console.log('尝试逐条插入作为备选方案...');
            for (const rule of batch) {
                try {
                    const { error: singleError } = await supabase
                        .from('rules')
                        .insert([rule]);
                    
                    if (singleError) {
                        console.log(`❌ 插入规则 "${rule.name}" 失败:`, singleError.message);
                    } else {
                        console.log(`✅ 成功插入规则: ${rule.name}`);
                    }
                } catch (singleErr) {
                    console.log(`❌ 插入规则 "${rule.name}" 异常:`, singleErr.message);
                }
            }
        }
        
        // 添加延迟以避免速率限制
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log(`🎉 批量插入完成！`);
    return true;
}

/**
 * 验证表结构
 */
async function verifyTableStructure() {
    console.log('🔍 验证表结构...');
    
    try {
        // 尝试查询表结构
        const { data, error } = await supabase
            .from('rules')
            .select('name, key_name, description, requirement_type, story_type, sub_type')
            .limit(1);
        
        if (error) {
            console.log('❌ 表结构验证失败:', error.message);
            return false;
        }
        
        console.log('✅ 表结构验证成功');
        return true;
    } catch (err) {
        console.log('❌ 表结构验证异常:', err.message);
        return false;
    }
}

/**
 * 读取新格式数据
 */
function loadNewFormatRules() {
    console.log('📂 读取新格式的JSON文件...\n');
    
    const newFormatFiles = [
        'ACC_PURPOSE_LGT.json',
        'ACC_SPECIAL_ENTITY_LGT.json',
        'SOW_BUSINESS_OWNER_LGT.json',
        'SOW_EMPLOY_LGT.json',
        'SOW_INHERITANCE_LGT.json',
        'SOW_INVESTMENT_LGT.json',
        'SOW_SALES_ASSETS_LGT.json',
        'SOW_SALES_BUSINESS_LGT.json',
        'SOW_SALES_REALESTATE_LGT.json',
        'UBO_ASSET_COMPOSITION_LGT.json',
        'UBO_FAMILY_LGT.json'
    ];
    
    const basePath = path.join(__dirname, '..', '..', '..', 'LGT_Rule_Mgmt_vs0714(1)', 'LGT_Rule_Mgmt_newformat');
    const allRules = [];
    
    newFormatFiles.forEach(filename => {
        const filePath = path.join(basePath, filename);
        
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  文件不存在: ${filename}`);
            return;
        }
        
        try {
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const data = JSON.parse(fileContent);
            
            if (data.rules && Array.isArray(data.rules)) {
                data.rules.forEach(rule => {
                    const dbRule = {
                        name: rule.rule_name,
                        key_name: rule.key_name,
                        description: rule.description,
                        requirement_type: rule.requirement_type,
                        rule_condition: rule.rule_condition || '',
                        story_type: rule.story_type,
                        sub_type: rule.sub_type,
                        status: rule.status,
                        risk: rule.risk || 0,
                        priority: rule.priority || 0,
                        reference_key_names: rule.reference_key_names || null,
                        red_flags: rule.red_flags || null
                    };
                    
                    allRules.push(dbRule);
                });
                
                console.log(`✅ 已读取 ${filename}: ${data.rules.length} 条规则`);
            }
        } catch (error) {
            console.log(`❌ 读取文件 ${filename} 时出错:`, error.message);
        }
    });
    
    console.log(`\n📊 总共读取到 ${allRules.length} 条规则\n`);
    return allRules;
}

/**
 * 验证插入结果
 */
async function verifyInsertedData() {
    console.log('\n📊 验证插入结果...');
    
    try {
        const { data, error, count } = await supabase
            .from('rules')
            .select('*', { count: 'exact' })
            .limit(5);
        
        if (error) {
            console.log('❌ 验证数据时出错:', error.message);
            return;
        }
        
        console.log(`📈 数据库中现有 ${count} 条规则`);
        
        if (data && data.length > 0) {
            console.log('\n前5条记录示例:');
            data.forEach((record, index) => {
                console.log(`${index + 1}. ${record.name} (${record.story_type}/${record.sub_type})`);
            });
        }
        
    } catch (err) {
        console.log('❌ 验证数据时出错:', err.message);
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 强制刷新并导入新格式规则数据...\n');
    
    // 1. 验证表结构
    const structureOk = await verifyTableStructure();
    if (!structureOk) {
        console.log('❌ 表结构验证失败，请确保已执行SQL更新脚本');
        return;
    }
    
    // 2. 读取数据
    const rules = loadNewFormatRules();
    if (rules.length === 0) {
        console.log('❌ 没有读取到任何规则数据');
        return;
    }
    
    // 3. 插入数据
    await insertRulesWithSQL(rules);
    
    // 4. 验证结果
    await verifyInsertedData();
    
    console.log('\n🎉 导入完成！');
}

// 执行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { insertRulesWithSQL, loadNewFormatRules };
