const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// === Supabase配置 ===
const supabaseUrl = 'https://zczyxyoklmxudtqqekfh.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpjenl4eW9rbG14dWR0cXFla2ZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNDMwNTYsImV4cCI6MjA2NzgxOTA1Nn0.Q1iFmfei6N_UcDZa99sr1j6frNgzDAsFhEeQUzhskiI';
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * 检查新格式JSON文件
 */
function checkNewFormatFiles() {
    console.log('📂 检查新格式JSON文件...\n');
    
    const newFormatFiles = [
        'ACC_PURPOSE_LGT.json',
        'ACC_SPECIAL_ENTITY_LGT.json',
        'SOW_BUSINESS_OWNER_LGT.json',
        'SOW_EMPLOY_LGT.json',
        'SOW_INHERITANCE_LGT.json',
        'SOW_INVESTMENT_LGT.json',
        'SOW_SALES_ASSETS_LGT.json',
        'SOW_SALES_BUSINESS_LGT.json',
        'SOW_SALES_REALESTATE_LGT.json',
        'UBO_ASSET_COMPOSITION_LGT.json',
        'UBO_FAMILY_LGT.json'
    ];
    
    const fileStats = [];
    let totalRules = 0;
    
    newFormatFiles.forEach(filename => {
        // 指向新格式文件的正确路径
        const filePath = path.join(__dirname, '..', '..', '..', 'LGT_Rule_Mgmt_vs0714(1)', 'LGT_Rule_Mgmt_newformat', filename);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ 文件不存在: ${filename}`);
            fileStats.push({ filename, status: 'missing', rules: 0 });
            return;
        }
        
        try {
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const data = JSON.parse(fileContent);
            
            if (data.rules && Array.isArray(data.rules)) {
                const ruleCount = data.rules.length;
                totalRules += ruleCount;
                
                console.log(`✅ ${filename}: ${ruleCount} 条规则`);
                fileStats.push({ filename, status: 'ok', rules: ruleCount });
                
                // 检查第一条规则的字段结构
                if (data.rules.length > 0) {
                    const firstRule = data.rules[0];
                    const requiredFields = ['id', 'rule_name', 'key_name', 'description', 'requirement_type', 'story_type', 'sub_type'];
                    const missingFields = requiredFields.filter(field => !(field in firstRule));
                    
                    if (missingFields.length > 0) {
                        console.log(`  ⚠️  缺少字段: ${missingFields.join(', ')}`);
                    }
                }
            } else {
                console.log(`❌ ${filename}: 格式错误，缺少 rules 数组`);
                fileStats.push({ filename, status: 'invalid_format', rules: 0 });
            }
        } catch (error) {
            console.log(`❌ ${filename}: 解析错误 - ${error.message}`);
            fileStats.push({ filename, status: 'parse_error', rules: 0 });
        }
    });
    
    console.log(`\n📊 总计: ${totalRules} 条规则`);
    return { fileStats, totalRules };
}

/**
 * 检查数据库表结构
 */
async function checkDatabaseTable() {
    console.log('\n🔍 检查数据库表结构...');
    
    try {
        // 检查表是否存在
        const { data, error } = await supabase
            .from('rules')
            .select('*')
            .limit(1);
        
        if (error) {
            if (error.message.includes('relation "rules" does not exist')) {
                console.log('❌ rules表不存在');
                console.log('💡 需要执行: new_format/sql/create_rules_table_new_format.sql');
                return { exists: false, compatible: false };
            } else {
                console.log('❌ 查询表时出错:', error.message);
                return { exists: false, compatible: false };
            }
        }
        
        console.log('✅ rules表存在');
        
        // 检查表结构兼容性
        if (data && data.length > 0) {
            const record = data[0];
            const requiredFields = ['id', 'name', 'key_name', 'description', 'requirement_type', 'story_type', 'sub_type', 'status', 'risk', 'priority'];
            const existingFields = Object.keys(record);
            const missingFields = requiredFields.filter(field => !existingFields.includes(field));
            
            if (missingFields.length > 0) {
                console.log('❌ 表结构不兼容，缺少字段:', missingFields.join(', '));
                console.log('💡 需要重新创建表结构');
                return { exists: true, compatible: false };
            } else {
                console.log('✅ 表结构兼容新格式');
                return { exists: true, compatible: true };
            }
        } else {
            // 表为空，尝试测试插入来检查结构
            const testRule = {
                name: 'Test Rule',
                key_name: 'test_key',
                description: 'Test Description',
                requirement_type: 'mandatory',
                rule_condition: 'Test Condition',
                story_type: 'TEST',
                sub_type: 'COMPLETENESS',
                status: 'active',
                risk: 0,
                priority: 0
            };
            
            const { data: insertData, error: insertError } = await supabase
                .from('rules')
                .insert([testRule])
                .select();
            
            if (insertError) {
                console.log('❌ 表结构测试失败:', insertError.message);
                return { exists: true, compatible: false };
            } else {
                console.log('✅ 表结构兼容新格式');
                
                // 删除测试数据
                await supabase
                    .from('rules')
                    .delete()
                    .eq('story_type', 'TEST');
                
                return { exists: true, compatible: true };
            }
        }
        
    } catch (err) {
        console.log('❌ 检查数据库时出错:', err.message);
        return { exists: false, compatible: false };
    }
}

/**
 * 检查现有数据
 */
async function checkExistingData() {
    console.log('\n📊 检查现有数据...');
    
    try {
        const { data, error, count } = await supabase
            .from('rules')
            .select('*', { count: 'exact' })
            .limit(10);
        
        if (error) {
            console.log('❌ 查询数据时出错:', error.message);
            return;
        }
        
        console.log(`📈 当前表中有 ${count} 条记录`);
        
        if (data && data.length > 0) {
            console.log('\n前10条记录示例:');
            data.forEach((record, index) => {
                console.log(`${index + 1}. ${record.name} (${record.story_type}/${record.sub_type})`);
            });
            
            // 统计数据
            const storyTypes = [...new Set(data.map(r => r.story_type))];
            const subTypes = [...new Set(data.map(r => r.sub_type))];
            
            console.log(`\n📊 数据概览:`);
            console.log(`  Story Types: ${storyTypes.join(', ')}`);
            console.log(`  Sub Types: ${subTypes.join(', ')}`);
        }
        
    } catch (err) {
        console.log('❌ 检查数据时出错:', err.message);
    }
}

/**
 * 生成兼容性报告
 */
function generateCompatibilityReport(fileCheck, dbCheck) {
    console.log('\n📋 兼容性报告');
    console.log('='.repeat(50));
    
    console.log('\n📂 文件检查结果:');
    console.log(`  总规则数: ${fileCheck.totalRules}`);
    const okFiles = fileCheck.fileStats.filter(f => f.status === 'ok').length;
    console.log(`  有效文件: ${okFiles}/${fileCheck.fileStats.length}`);
    
    console.log('\n🗄️  数据库检查结果:');
    console.log(`  表存在: ${dbCheck.exists ? '✅' : '❌'}`);
    console.log(`  结构兼容: ${dbCheck.compatible ? '✅' : '❌'}`);
    
    console.log('\n🎯 建议操作:');
    if (!dbCheck.exists) {
        console.log('  1. 执行 new_format/sql/create_rules_table_new_format.sql 创建表');
        console.log('  2. 运行 node new_format/scripts/import_new_format_rules.js 导入数据');
    } else if (!dbCheck.compatible) {
        console.log('  1. 备份现有数据（如果需要）');
        console.log('  2. 执行 new_format/sql/create_rules_table_new_format.sql 重建表');
        console.log('  3. 运行 node new_format/scripts/import_new_format_rules.js 导入数据');
    } else {
        console.log('  1. 运行 node new_format/scripts/import_new_format_rules.js 导入数据');
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🔍 新格式数据兼容性检查\n');
    
    // 1. 检查文件
    const fileCheck = checkNewFormatFiles();
    
    // 2. 检查数据库
    const dbCheck = await checkDatabaseTable();
    
    // 3. 检查现有数据
    await checkExistingData();
    
    // 4. 生成报告
    generateCompatibilityReport(fileCheck, dbCheck);
}

// 执行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { checkNewFormatFiles, checkDatabaseTable };
