const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// === Supabase配置 ===
const supabaseUrl = 'https://zczyxyoklmxudtqqekfh.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpjenl4eW9rbG14dWR0cXFla2ZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNDMwNTYsImV4cCI6MjA2NzgxOTA1Nn0.Q1iFmfei6N_UcDZa99sr1j6frNgzDAsFhEeQUzhskiI';

// 创建新的客户端实例，强制清除缓存
function createFreshSupabaseClient() {
    return createClient(supabaseUrl, supabaseKey, {
        db: {
            schema: 'public'
        },
        auth: {
            persistSession: false
        },
        global: {
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        }
    });
}

/**
 * 读取新格式数据
 */
function loadNewFormatRules() {
    console.log('📂 读取新格式的JSON文件...\n');
    
    const newFormatFiles = [
        'ACC_PURPOSE_LGT.json',
        'ACC_SPECIAL_ENTITY_LGT.json', 
        'SOW_BUSINESS_OWNER_LGT.json',
        'SOW_EMPLOY_LGT.json',
        'SOW_INHERITANCE_LGT.json',
        'SOW_INVESTMENT_LGT.json',
        'SOW_SALES_ASSETS_LGT.json',
        'SOW_SALES_BUSINESS_LGT.json',
        'SOW_SALES_REALESTATE_LGT.json',
        'UBO_ASSET_COMPOSITION_LGT.json',
        'UBO_FAMILY_LGT.json'
    ];
    
    const allRules = [];
    
    newFormatFiles.forEach(filename => {
        // 检查多个可能的路径
        const possiblePaths = [
            path.join(process.cwd(), filename),
            path.join(__dirname, '..', '..', '..', 'LGT_Rule_Mgmt_vs0714(1)', 'LGT_Rule_Mgmt_newformat', filename),
            path.join('C:', 'Users', 'halina.han', 'Desktop', 'LGT_Rule_Mgmt_vs0714(1)', 'LGT_Rule_Mgmt_newformat', filename)
        ];

        let filePath = null;
        for (const testPath of possiblePaths) {
            if (fs.existsSync(testPath)) {
                filePath = testPath;
                break;
            }
        }

        if (!filePath) {
            console.log(`⚠️  文件不存在: ${filename}`);
            return;
        }
        
        try {
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const data = JSON.parse(fileContent);
            
            if (data.rules && Array.isArray(data.rules)) {
                data.rules.forEach(rule => {
                    const dbRule = {
                        name: rule.rule_name, // rule_name → name
                        key_name: rule.key_name,
                        description: rule.description,
                        requirement_type: rule.requirement_type,
                        rule_condition: rule.rule_condition || '',
                        story_type: rule.story_type,
                        sub_type: rule.sub_type,
                        status: rule.status,
                        risk: rule.risk || 0,
                        priority: rule.priority || 0,
                        reference_key_names: rule.reference_key_names || null,
                        red_flags: rule.red_flags || null
                    };
                    
                    allRules.push(dbRule);
                });
                
                console.log(`✅ 已读取 ${filename}: ${data.rules.length} 条规则`);
            }
        } catch (error) {
            console.log(`❌ 读取文件 ${filename} 时出错:`, error.message);
        }
    });
    
    console.log(`\n📊 总共读取到 ${allRules.length} 条规则\n`);
    return allRules;
}

/**
 * 使用原生SQL插入数据，避免缓存问题
 */
async function insertWithRawSQL(rules) {
    console.log('📥 使用原生SQL插入数据（避免缓存问题）...');
    
    const supabase = createFreshSupabaseClient();
    
    // 先清空现有数据
    console.log('🗑️  清空现有数据...');
    try {
        const { error: deleteError } = await supabase
            .from('rules')
            .delete()
            .neq('id', 0);
        
        if (deleteError) {
            console.log('❌ 清空数据失败:', deleteError.message);
            return false;
        }
        console.log('✅ 现有数据已清空');
    } catch (err) {
        console.log('❌ 清空数据异常:', err.message);
        return false;
    }
    
    // 分批插入数据
    const batchSize = 10;
    const totalBatches = Math.ceil(rules.length / batchSize);
    let successCount = 0;
    
    for (let i = 0; i < totalBatches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, rules.length);
        const batch = rules.slice(start, end);
        
        console.log(`📥 插入第 ${i + 1}/${totalBatches} 批数据 (${batch.length} 条)...`);
        
        // 逐条插入以避免批量插入的缓存问题
        for (const rule of batch) {
            try {
                const { data, error } = await supabase
                    .from('rules')
                    .insert([rule])
                    .select();
                
                if (error) {
                    console.log(`❌ 插入规则 "${rule.name}" 失败:`, error.message);
                } else {
                    successCount++;
                    if (successCount % 10 === 0) {
                        console.log(`✅ 已成功插入 ${successCount} 条规则`);
                    }
                }
            } catch (err) {
                console.log(`❌ 插入规则 "${rule.name}" 异常:`, err.message);
            }
            
            // 添加小延迟避免速率限制
            await new Promise(resolve => setTimeout(resolve, 50));
        }
    }
    
    console.log(`🎉 插入完成！成功插入 ${successCount}/${rules.length} 条规则`);
    return successCount > 0;
}

/**
 * 验证表结构
 */
async function verifyTableStructure() {
    console.log('🔍 验证表结构...');
    
    const supabase = createFreshSupabaseClient();
    
    try {
        // 尝试插入一条测试数据来验证表结构
        const testRule = {
            name: 'Test Rule',
            key_name: 'test_key_' + Date.now(),
            description: 'Test Description',
            requirement_type: 'mandatory',
            rule_condition: 'Test Condition',
            story_type: 'TEST',
            sub_type: 'COMPLETENESS',
            status: 'active',
            risk: 0,
            priority: 0,
            reference_key_names: null,
            red_flags: null
        };
        
        const { data, error } = await supabase
            .from('rules')
            .insert([testRule])
            .select();
        
        if (error) {
            console.log('❌ 表结构验证失败:', error.message);
            console.log('💡 请确保已执行SQL更新脚本：sql_files/simple_update_for_new_format.sql');
            return false;
        }
        
        // 删除测试数据
        if (data && data.length > 0) {
            await supabase
                .from('rules')
                .delete()
                .eq('id', data[0].id);
        }
        
        console.log('✅ 表结构验证成功');
        return true;
    } catch (err) {
        console.log('❌ 表结构验证异常:', err.message);
        return false;
    }
}

/**
 * 验证插入结果
 */
async function verifyInsertedData() {
    console.log('\n📊 验证插入结果...');
    
    const supabase = createFreshSupabaseClient();
    
    try {
        const { data, error, count } = await supabase
            .from('rules')
            .select('*', { count: 'exact' })
            .limit(5);
        
        if (error) {
            console.log('❌ 验证数据时出错:', error.message);
            return;
        }
        
        console.log(`📈 数据库中现有 ${count} 条规则`);
        
        if (data && data.length > 0) {
            console.log('\n前5条记录示例:');
            data.forEach((record, index) => {
                console.log(`${index + 1}. ${record.name} (${record.story_type}/${record.sub_type})`);
            });
        }
        
        // 按story_type统计
        const { data: storyTypes } = await supabase
            .from('rules')
            .select('story_type')
            .neq('story_type', 'TEST');
        
        if (storyTypes) {
            const counts = {};
            storyTypes.forEach(item => {
                counts[item.story_type] = (counts[item.story_type] || 0) + 1;
            });
            
            console.log('\n📊 按story_type统计:');
            Object.entries(counts).forEach(([type, count]) => {
                console.log(`  ${type}: ${count} 条`);
            });
        }
        
    } catch (err) {
        console.log('❌ 验证数据时出错:', err.message);
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 清除缓存并导入新格式规则数据...\n');
    
    // 1. 验证表结构
    const structureOk = await verifyTableStructure();
    if (!structureOk) {
        return;
    }
    
    // 2. 读取数据
    const rules = loadNewFormatRules();
    if (rules.length === 0) {
        console.log('❌ 没有读取到任何规则数据');
        return;
    }
    
    // 3. 插入数据
    const inserted = await insertWithRawSQL(rules);
    if (!inserted) {
        return;
    }
    
    // 4. 验证结果
    await verifyInsertedData();
    
    console.log('\n🎉 导入完成！');
}

// 执行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { loadNewFormatRules, insertWithRawSQL };
