{"name": "lgt-rules-new-format", "version": "1.0.0", "description": "LGT规则管理系统 - 新格式支持", "main": "scripts/import_new_format_rules.js", "scripts": {"check": "node scripts/check_new_format_data.js", "import": "node scripts/import_new_format_rules.js", "auto-import": "node scripts/auto_detect_and_import.js", "import-with-id": "node scripts/import_with_json_id.js", "test": "npm run check"}, "dependencies": {"@supabase/supabase-js": "^2.50.5"}, "devDependencies": {"supabase": "^2.30.4"}, "keywords": ["kyc", "rules", "supabase", "lgt"], "author": "LGT Team", "license": "ISC"}