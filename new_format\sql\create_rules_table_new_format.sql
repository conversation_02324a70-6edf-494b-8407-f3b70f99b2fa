-- =====================================================
-- 创建新格式的rules表结构
-- 适配同事提供的新JSON格式
-- =====================================================

-- 1. 删除现有的rules表（如果存在）
DROP TABLE IF EXISTS rules CASCADE;

-- 2. 创建新的rules表结构以匹配新格式
CREATE TABLE rules (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    key_name TEXT NOT NULL,
    description TEXT,
    requirement_type TEXT NOT NULL CHECK (requirement_type IN ('mandatory', 'conditional')),
    rule_condition TEXT,
    story_type TEXT NOT NULL,
    sub_type TEXT NOT NULL CHECK (sub_type IN ('COMPLETENESS', 'LOGICAL_VALIDATION')),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    risk INTEGER DEFAULT 0,
    priority INTEGER DEFAULT 0,
    reference_key_names JSONB,
    red_flags JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建索引以提高查询性能
CREATE INDEX idx_rules_story_type ON rules(story_type);
CREATE INDEX idx_rules_sub_type ON rules(sub_type);
CREATE INDEX idx_rules_status ON rules(status);
CREATE INDEX idx_rules_key_name ON rules(key_name);
CREATE INDEX idx_rules_name ON rules(name);
CREATE INDEX idx_rules_requirement_type ON rules(requirement_type);

-- 4. 为JSONB字段创建GIN索引
CREATE INDEX idx_rules_reference_key_names ON rules USING GIN (reference_key_names);
CREATE INDEX idx_rules_red_flags ON rules USING GIN (red_flags);

-- 5. 创建更新时间的触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_rules_updated_at 
    BEFORE UPDATE ON rules 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 6. 添加表注释
COMMENT ON TABLE rules IS '规则表 - 存储KYC相关的业务规则（新格式）';
COMMENT ON COLUMN rules.id IS '主键ID';
COMMENT ON COLUMN rules.name IS '规则名称';
COMMENT ON COLUMN rules.key_name IS '规则的唯一标识键';
COMMENT ON COLUMN rules.description IS '规则描述';
COMMENT ON COLUMN rules.requirement_type IS '需求类型: mandatory(强制) 或 conditional(条件)';
COMMENT ON COLUMN rules.rule_condition IS '规则触发条件';
COMMENT ON COLUMN rules.story_type IS '规则所属的业务场景类型';
COMMENT ON COLUMN rules.sub_type IS '规则子类型: COMPLETENESS(完整性检查) 或 LOGICAL_VALIDATION(逻辑验证)';
COMMENT ON COLUMN rules.status IS '规则状态: active(激活) 或 inactive(未激活)';
COMMENT ON COLUMN rules.risk IS '风险等级';
COMMENT ON COLUMN rules.priority IS '规则优先级';
COMMENT ON COLUMN rules.reference_key_names IS '引用的其他规则键名（JSON数组）';
COMMENT ON COLUMN rules.red_flags IS '红旗标识（JSON数组）';
COMMENT ON COLUMN rules.created_at IS '创建时间';
COMMENT ON COLUMN rules.updated_at IS '最后更新时间';

-- 7. 创建统计视图
CREATE VIEW rules_summary AS
SELECT 
    story_type,
    sub_type,
    requirement_type,
    status,
    COUNT(*) as rule_count
FROM rules 
GROUP BY story_type, sub_type, requirement_type, status
ORDER BY story_type, sub_type, requirement_type;

COMMENT ON VIEW rules_summary IS '规则统计视图 - 按各维度分组统计';

-- 8. 创建详细统计视图
CREATE VIEW rules_detailed_summary AS
SELECT 
    story_type,
    sub_type,
    COUNT(*) as total_rules,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_rules,
    COUNT(CASE WHEN requirement_type = 'mandatory' THEN 1 END) as mandatory_rules,
    COUNT(CASE WHEN requirement_type = 'conditional' THEN 1 END) as conditional_rules,
    AVG(priority) as avg_priority,
    AVG(risk) as avg_risk
FROM rules 
GROUP BY story_type, sub_type
ORDER BY story_type, sub_type;

COMMENT ON VIEW rules_detailed_summary IS '规则详细统计视图 - 包含各种统计指标';

-- 9. 验证表创建成功
SELECT 'Rules table created successfully for new format!' as status;
